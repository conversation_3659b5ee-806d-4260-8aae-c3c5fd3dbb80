package com.zhuanzhuan.qc.report.outbound.contract.facade.report;

import com.bj58.spat.scf.server.contract.annotation.ServiceContract;
import com.bj58.zhuanzhuan.arch.common.api.ApiResult;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveRecyclingReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveRecyclingReportDTO;

@ServiceContract
public interface IOutboundReportFacade {

    /**
     * 保存回收质检报告
     *
     * @param cmd 保存回收质检报告请求
     * @return 保存回收质检报告结果
     */
    ApiResult<SaveRecyclingReportDTO> saveRecyclingReport(SaveRecyclingReportCmd cmd);

}
