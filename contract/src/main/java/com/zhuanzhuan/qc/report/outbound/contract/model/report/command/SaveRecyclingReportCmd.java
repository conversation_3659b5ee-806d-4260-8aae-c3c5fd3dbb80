package com.zhuanzhuan.qc.report.outbound.contract.model.report.command;

import com.zhuanzhuan.qc.report.outbound.contract.enums.report.EOutboundReportType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SaveRecyclingReportCmd extends BaseOutboundReportCmd {

    @Override
    public EOutboundReportType getReportType() {
        return EOutboundReportType.RECYCLING;
    }

}
