package com.zhuanzhuan.qc.report.outbound.domain.report.service;

import com.zhuanzhuan.qc.report.outbound.BaseTest;
import com.zhuanzhuan.qc.report.outbound.contract.enums.report.EOutboundReportType;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveRecyclingReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveRecyclingReportDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 出库报告服务测试
 * 演示基于模板方法模式和策略模式的报告保存流程
 */
@Slf4j
public class OutboundReportServiceTest extends BaseTest {

    @Autowired
    private IOutboundReportService outboundReportService;

    @Test
    public void testSaveRecyclingReport() {
        // 准备测试数据
        SaveRecyclingReportCmd cmd = new SaveRecyclingReportCmd();
        cmd.setQcCode(123456L);
        cmd.setBusinessLineId(1L);
        cmd.setOperatorId(1001L);
        cmd.setOperatorName("测试操作员");
        
        // 验证报告类型
        assertEquals(EOutboundReportType.RECYCLING_REPORT, cmd.getReportType());
        
        // 执行保存操作
        log.info("开始测试回收报告保存流程");
        SaveRecyclingReportDTO result = outboundReportService.saveRecyclingReport(cmd);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(cmd.getQcCode(), result.getQcCode());
        
        log.info("回收报告保存测试完成, qcCode: {}", result.getQcCode());
    }
    
    /**
     * 测试流程的可扩展性
     * 演示如何通过添加新的策略来支持新的报告类型
     */
    @Test
    public void testReportSaveFlowExtensibility() {
        log.info("演示报告保存流程的可扩展性:");
        log.info("1. 统一的8步保存流程确保所有报告类型的一致性");
        log.info("2. 策略模式允许每种报告类型有自己的特定实现");
        log.info("3. 模板方法模式确保流程的稳定性和可维护性");
        log.info("4. 上下文对象提供灵活的数据传递和状态管理");
        log.info("5. 工厂模式简化策略的获取和管理");
        
        // 这里可以添加更多的测试用例来验证不同报告类型的保存流程
    }
}
