package com.zhuanzhuan.qc.report.outbound.domain.report.service.factory;

import com.zhuanzhuan.qc.report.outbound.contract.enums.report.EOutboundReportType;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.OutboundReportSaveStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * 出库报告保存策略工厂
 * 负责管理和获取不同类型的出库报告保存策略
 */
@Slf4j
@Component
public class OutboundReportSaveStrategyFactory {

    @Autowired
    private List<OutboundReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO>> strategies;

    private final Map<EOutboundReportType, OutboundReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO>> strategyMap = new EnumMap<>(EOutboundReportType.class);

    @PostConstruct
    public void init() {
        for (OutboundReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO> strategy : strategies) {
            EOutboundReportType reportType = strategy.getSupportedReportType();
            strategyMap.put(reportType, strategy);
            log.info("注册出库报告保存策略: {} -> {}", reportType, strategy.getClass().getSimpleName());
        }
        log.info("出库报告保存策略工厂初始化完成，共注册 {} 个策略", strategyMap.size());
    }

    /**
     * 根据报告类型获取对应的保存策略
     *
     * @param reportType 报告类型
     * @return 对应的保存策略
     * @throws IllegalArgumentException 如果找不到对应的策略
     */
    @SuppressWarnings("unchecked")
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> OutboundReportSaveStrategy<T, R> getStrategy(EOutboundReportType reportType) {
        OutboundReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO> strategy = strategyMap.get(reportType);
        if (strategy == null) {
            throw new IllegalArgumentException("未找到报告类型 [" + reportType + "] 对应的保存策略");
        }
        return (OutboundReportSaveStrategy<T, R>) strategy;
    }

    /**
     * 检查是否支持指定的报告类型
     *
     * @param reportType 报告类型
     * @return 是否支持
     */
    public boolean isSupported(EOutboundReportType reportType) {
        return strategyMap.containsKey(reportType);
    }

    /**
     * 获取所有支持的报告类型
     *
     * @return 支持的报告类型集合
     */
    public java.util.Set<EOutboundReportType> getSupportedReportTypes() {
        return strategyMap.keySet();
    }
}
