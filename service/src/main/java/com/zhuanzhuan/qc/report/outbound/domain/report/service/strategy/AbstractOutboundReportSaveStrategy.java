package com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.OutboundReportSaveContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 抽象出库报告保存策略
 * 提供通用的实现和模板方法，减少子类重复代码
 */
@Slf4j
public abstract class AbstractOutboundReportSaveStrategy<T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> 
        implements OutboundReportSaveStrategy<T, R> {

    @Override
    public void preValidate(OutboundReportSaveContext<T, R> context) {
        log.debug("执行通用前置校验, qcCode: {}, reportType: {}", 
                context.getCommand().getQcCode(), context.getReportType());
        
        // 基本参数校验
        T command = context.getCommand();
        if (command.getQcCode() == null) {
            throw new IllegalArgumentException("质检码不能为空");
        }
        if (command.getBusinessLineId() == null) {
            throw new IllegalArgumentException("业务线ID不能为空");
        }
        if (command.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        if (!StringUtils.hasText(command.getOperatorName())) {
            throw new IllegalArgumentException("操作人姓名不能为空");
        }
        
        // 子类特定校验
        doPreValidate(context);
        
        log.debug("通用前置校验完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void preProcess(OutboundReportSaveContext<T, R> context) {
        log.debug("执行通用前置处理, qcCode: {}, reportType: {}", 
                context.getCommand().getQcCode(), context.getReportType());
        
        // 初始化结果对象
        if (context.getResult() == null) {
            context.setResult(createResult());
        }
        
        // 设置基本属性到上下文
        T command = context.getCommand();
        context.setAttribute("qcCode", command.getQcCode());
        context.setAttribute("businessLineId", command.getBusinessLineId());
        context.setAttribute("operatorId", command.getOperatorId());
        context.setAttribute("operatorName", command.getOperatorName());
        context.setAttribute("reportType", context.getReportType());
        
        // 子类特定处理
        doPreProcess(context);
        
        log.debug("通用前置处理完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void processReport(OutboundReportSaveContext<T, R> context) {
        log.debug("执行通用报告处理, qcCode: {}", context.getCommand().getQcCode());
        
        // 子类特定处理
        doProcessReport(context);
        
        log.debug("通用报告处理完成, qcCode: {}", context.getCommand().getQcCode());
    }

    @Override
    public void writeData(OutboundReportSaveContext<T, R> context) {
        log.debug("执行通用数据写入, qcCode: {}", context.getCommand().getQcCode());
        
        // 确保结果对象存在
        if (context.getResult() == null) {
            context.setResult(createResult());
        }
        
        // 设置基本返回信息
        R result = context.getResult();
        result.setQcCode(context.getCommand().getQcCode());
        
        // 子类特定数据写入
        doWriteData(context);
        
        log.debug("通用数据写入完成, qcCode: {}", context.getCommand().getQcCode());
    }

    @Override
    public void handleException(OutboundReportSaveContext<T, R> context, Exception e) {
        log.error("报告保存过程中发生异常, qcCode: {}, reportType: {}, currentStep: {}, error: {}", 
                context.getCommand().getQcCode(), context.getReportType(), 
                context.getCurrentStep(), e.getMessage(), e);
        
        // 子类特定异常处理
        doHandleException(context, e);
    }

    /**
     * 子类可重写的前置校验方法
     */
    protected void doPreValidate(OutboundReportSaveContext<T, R> context) {
        // 默认空实现
    }

    /**
     * 子类可重写的前置处理方法
     */
    protected void doPreProcess(OutboundReportSaveContext<T, R> context) {
        // 默认空实现
    }

    /**
     * 子类可重写的报告处理方法
     */
    protected void doProcessReport(OutboundReportSaveContext<T, R> context) {
        // 默认空实现
    }

    /**
     * 子类可重写的数据写入方法
     */
    protected void doWriteData(OutboundReportSaveContext<T, R> context) {
        // 默认空实现
    }

    /**
     * 子类可重写的异常处理方法
     */
    protected void doHandleException(OutboundReportSaveContext<T, R> context, Exception e) {
        // 默认重新抛出异常
        if (e instanceof RuntimeException) {
            throw (RuntimeException) e;
        } else {
            throw new RuntimeException("报告保存失败", e);
        }
    }

    /**
     * 获取报告类型名称（用于日志等）
     */
    protected String getReportTypeName() {
        return getSupportedReportType().name();
    }
}
