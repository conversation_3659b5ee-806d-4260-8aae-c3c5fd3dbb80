# 出库报告保存服务架构设计

## 概述

基于模板方法模式和策略模式设计的可复用、可扩展的出库报告保存架构，实现了统一的8步保存流程，支持多种报告类型的个性化处理。

## 设计模式

### 1. 模板方法模式 (Template Method Pattern)
- **类**: `OutboundReportSaveTemplate`
- **作用**: 定义统一的8步保存流程骨架，确保所有报告类型遵循相同的处理步骤
- **优势**: 流程稳定、易维护、避免重复代码

### 2. 策略模式 (Strategy Pattern)
- **接口**: `OutboundReportSaveStrategy`
- **作用**: 为不同报告类型提供个性化的实现逻辑
- **优势**: 易扩展、符合开闭原则、降低耦合

### 3. 工厂模式 (Factory Pattern)
- **类**: `OutboundReportSaveStrategyFactory`
- **作用**: 管理和获取不同类型的报告保存策略
- **优势**: 简化对象创建、集中管理策略

## 核心组件

### 1. 上下文对象 (Context)
```java
OutboundReportSaveContext<T, R>
```
- 贯穿整个保存流程的数据载体
- 支持灵活的属性管理和状态控制
- 提供跳过步骤、错误处理等功能

### 2. 模板类 (Template)
```java
OutboundReportSaveTemplate
```
- 定义8步标准保存流程
- 统一异常处理和日志记录
- 支持步骤跳过和性能监控

### 3. 策略接口 (Strategy Interface)
```java
OutboundReportSaveStrategy<T, R>
```
- 定义8个核心步骤的接口
- 提供默认实现和扩展点
- 支持异常处理和结果创建

### 4. 抽象策略基类 (Abstract Strategy)
```java
AbstractOutboundReportSaveStrategy<T, R>
```
- 提供通用实现逻辑
- 减少子类重复代码
- 统一基础校验和处理

## 8步保存流程

1. **前置校验 (preValidate)**
   - 校验输入参数的合法性
   - 验证业务规则和约束条件

2. **前置处理 (preProcess)**
   - 初始化结果对象
   - 设置基础属性到上下文

3. **基本信息处理 (processBasicInfo)**
   - 处理报告的基础信息
   - 获取相关业务数据

4. **模板查询 (queryTemplate)**
   - 根据业务参数查询质检模板
   - 设置模板到上下文

5. **报告生成 (generateReport)**
   - 根据模板和输入数据生成报告内容
   - 处理质检项目和结果

6. **报告处理 (processReport)**
   - 对生成的报告进行进一步加工
   - 计算评分、等级等

7. **报告映射 (mapReport)**
   - 将报告数据映射到返回DTO对象
   - 设置版本号、状态等信息

8. **后置处理 (postProcess)**
   - 发送通知、更新状态
   - 记录操作日志

9. **数据写入 (writeData)**
   - 持久化报告数据到数据库
   - 更新相关业务状态

## 使用示例

### 1. 实现新的报告类型策略

```java
@Component
public class NewReportSaveStrategy extends AbstractOutboundReportSaveStrategy<NewReportCmd, NewReportDTO> {
    
    @Override
    public EOutboundReportType getSupportedReportType() {
        return EOutboundReportType.NEW_REPORT;
    }
    
    @Override
    public void processBasicInfo(OutboundReportSaveContext<NewReportCmd, NewReportDTO> context) {
        // 实现特定的基本信息处理逻辑
    }
    
    @Override
    public void queryTemplate(OutboundReportSaveContext<NewReportCmd, NewReportDTO> context) {
        // 实现特定的模板查询逻辑
    }
    
    // ... 其他步骤的实现
}
```

### 2. 调用保存服务

```java
@Service
public class SomeService {
    
    @Autowired
    private IOutboundReportService outboundReportService;
    
    public void saveReport() {
        SaveRecyclingReportCmd cmd = new SaveRecyclingReportCmd();
        cmd.setQcCode(123456L);
        cmd.setBusinessLineId(1L);
        cmd.setOperatorId(1001L);
        cmd.setOperatorName("操作员");
        
        SaveRecyclingReportDTO result = outboundReportService.saveRecyclingReport(cmd);
    }
}
```

## 扩展性

### 1. 添加新报告类型
1. 创建新的Command和DTO类
2. 在枚举中添加新的报告类型
3. 实现对应的Strategy类
4. 在Service中添加新的方法

### 2. 自定义处理步骤
- 重写AbstractOutboundReportSaveStrategy中的do*方法
- 使用上下文的skipFlag跳过不需要的步骤
- 通过attributes传递自定义数据

### 3. 异常处理
- 重写handleException方法实现自定义异常处理
- 使用上下文的markFailed方法标记失败状态
- 支持步骤级别的异常恢复

## 优势

1. **统一性**: 所有报告类型遵循相同的处理流程
2. **可扩展性**: 易于添加新的报告类型和处理逻辑
3. **可维护性**: 清晰的职责分离和模块化设计
4. **可测试性**: 每个组件都可以独立测试
5. **性能监控**: 内置的性能监控和日志记录
6. **错误处理**: 完善的异常处理和恢复机制

## 注意事项

1. 策略类需要添加@Component注解以便Spring自动注册
2. 确保EOutboundReportType枚举与策略类的getSupportedReportType()方法返回值一致
3. 合理使用上下文的attributes避免内存泄漏
4. 在实现具体业务逻辑时注意异常处理和日志记录
