package com.zhuanzhuan.qc.report.outbound.domain.report.saver;

import com.zhuanzhuan.qc.report.outbound.contract.enums.report.EOutboundReportType;
import com.zhuanzhuan.qc.report.outbound.infrastructure.manager.model.QcItemInfoBO;
import com.zhuanzhuan.qc.report.outbound.infrastructure.manager.model.QcTemplateBO;
import lombok.Data;

/**
 * 出库报告保存上下文
 * 在整个保存流程中传递数据和状态，支持可扩展的属性管理
 */
@Data
public class OutboundReportSaveContext {

    private EOutboundReportType outboundReportType;

    /**
     * 质检项信息
     */
    private QcItemInfoBO qcItemInfoBO;

    /**
     * 质检模板
     */
    private QcTemplateBO template;

    /**
     * 获取报告类型
     */
    public EOutboundReportType getReportType() {
        return outboundReportType;
    }

}
