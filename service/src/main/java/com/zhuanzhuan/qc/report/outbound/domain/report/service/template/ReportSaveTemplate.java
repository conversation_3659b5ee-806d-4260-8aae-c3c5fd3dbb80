package com.zhuanzhuan.qc.report.outbound.domain.report.service.template;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.ReportSaveContext;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.processor.ReportSaveProcessor;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.ReportSaveStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 报告保存模板方法类
 * 定义统一的保存流程骨架，具体实现由策略模式处理
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ReportSaveTemplate {

    private final ReportSaveProcessor reportSaveProcessor;

    /**
     * 模板方法：定义报告保存的标准流程
     *
     * @param cmd      保存命令
     * @param strategy 保存策略
     * @return 保存结果
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> R saveReport(T cmd, ReportSaveStrategy<T, R> strategy) {
        // 创建上下文
        ReportSaveContext<T, R> context = new ReportSaveContext<>(cmd, strategy);

        // 1. 前置校验
        reportSaveProcessor.preValidate(context);

        // 2. 前置处理
        reportSaveProcessor.preProcess(context);

        // 3. 基本信息处理
        reportSaveProcessor.processBasicInfo(context);

        // 4. 模板查询
        reportSaveProcessor.queryTemplate(context);

        // 5. 报告生成
        reportSaveProcessor.generateReport(context);

        // 6. 报告处理
        reportSaveProcessor.processReport(context);

        // 7. 报告映射
        reportSaveProcessor.mapReport(context);

        // 8. 后置处理数据写入
        reportSaveProcessor.postProcess(context);

        // 9. 数据写入
        reportSaveProcessor.writeData(context);

        return context.getResult();
    }
}
