package com.zhuanzhuan.qc.report.outbound.domain.report.service.context;

import com.zhuanzhuan.qc.report.outbound.contract.enums.report.EOutboundReportType;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.ReportSaveStrategy;
import com.zhuanzhuan.qc.report.outbound.infrastructure.manager.model.QcItemInfoBO;
import com.zhuanzhuan.qc.report.outbound.infrastructure.manager.model.QcTemplateBO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 报告保存上下文
 * 在整个保存流程中传递数据和状态
 */
@Data
public class ReportSaveContext<T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> {
    
    /**
     * 输入命令
     */
    private final T command;
    
    /**
     * 处理结果
     */
    private R result;

    /**
     * 保存策略
     */
    private final ReportSaveStrategy<T, R> strategy;

    /**
     * 质检项信息
     */
    private QcItemInfoBO qcItemInfoBO;

    /**
     * 质检模板
     */
    private QcTemplateBO template;
    
    /**
     * 获取报告类型
     */
    public EOutboundReportType getReportType() {
        return command.getReportType();
    }
}
