package com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.ReportSaveContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 抽象报告保存策略
 * 提供通用的实现和模板方法
 */
@Slf4j
public abstract class AbstractReportSaveStrategy<T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> 
        implements ReportSaveStrategy<T, R> {

    @Override
    public void preValidate(ReportSaveContext<T, R> context) {
        log.debug("执行默认前置校验, qcCode: {}", context.getCommand().getQcCode());
        
        // 基本参数校验
        T command = context.getCommand();
        if (command.getQcCode() == null) {
            throw new IllegalArgumentException("质检码不能为空");
        }
        if (command.getBusinessLineId() == null) {
            throw new IllegalArgumentException("业务线ID不能为空");
        }
        if (command.getOperatorId() == null) {
            throw new IllegalArgumentException("操作人ID不能为空");
        }
        if (!StringUtils.hasText(command.getOperatorName())) {
            throw new IllegalArgumentException("操作人姓名不能为空");
        }
        
        // 子类可以重写进行额外校验
        doPreValidate(context);
    }

    @Override
    public void preProcess(ReportSaveContext<T, R> context) {
        log.debug("执行默认前置处理, qcCode: {}", context.getCommand().getQcCode());
        
        // 初始化结果对象
        context.setResult(createResult());
        
        // 子类可以重写进行额外处理
        doPreProcess(context);
    }

    @Override
    public void processReport(ReportSaveContext<T, R> context) {
        log.debug("执行默认报告处理, qcCode: {}", context.getCommand().getQcCode());
        
        // 子类可以重写进行具体处理
        doProcessReport(context);
    }

    @Override
    public void handleException(ReportSaveContext<T, R> context, Exception e) {
        log.error("报告保存过程中发生异常, qcCode: {}, currentStep: {}, error: {}", 
                context.getCommand().getQcCode(), context.getCurrentStep(), e.getMessage(), e);
        
        // 子类可以重写进行具体异常处理
        doHandleException(context, e);
    }

    /**
     * 子类可重写的前置校验方法
     */
    protected void doPreValidate(ReportSaveContext<T, R> context) {
        // 默认空实现
    }

    /**
     * 子类可重写的前置处理方法
     */
    protected void doPreProcess(ReportSaveContext<T, R> context) {
        // 默认空实现
    }

    /**
     * 子类可重写的报告处理方法
     */
    protected void doProcessReport(ReportSaveContext<T, R> context) {
        // 默认空实现
    }

    /**
     * 子类可重写的异常处理方法
     */
    protected void doHandleException(ReportSaveContext<T, R> context, Exception e) {
        // 默认重新抛出异常
        if (e instanceof RuntimeException) {
            throw (RuntimeException) e;
        } else {
            throw new RuntimeException("报告保存失败", e);
        }
    }

    /**
     * 获取报告类型名称（去掉Save和Cmd后缀）
     */
    protected String getReportTypeName() {
        String className = this.getClass().getSimpleName();
        return className.replace("ReportSaveStrategy", "");
    }
}
