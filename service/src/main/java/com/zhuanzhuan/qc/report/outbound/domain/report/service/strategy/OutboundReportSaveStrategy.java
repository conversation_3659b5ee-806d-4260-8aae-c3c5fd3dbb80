package com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy;

import com.zhuanzhuan.qc.report.outbound.contract.enums.report.EOutboundReportType;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.OutboundReportSaveContext;

/**
 * 出库报告保存策略接口
 * 定义不同报告类型的具体保存逻辑
 * 
 * 实现8步保存流程：
 * 1. 前置校验 - 校验输入参数和业务规则
 * 2. 前置处理 - 初始化和准备工作
 * 3. 基本信息处理 - 处理报告基础信息
 * 4. 模板查询 - 获取质检模板
 * 5. 报告生成 - 根据模板生成报告内容
 * 6. 报告处理 - 对生成的报告进行加工处理
 * 7. 报告映射 - 将报告数据映射到返回对象
 * 8. 后置处理 - 完成后的清理和通知工作
 * 9. 数据写入 - 持久化数据
 */
public interface OutboundReportSaveStrategy<T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> {
    
    /**
     * 获取策略支持的报告类型
     */
    EOutboundReportType getSupportedReportType();
    
    /**
     * 1. 前置校验
     * 校验输入参数的合法性和业务规则
     */
    default void preValidate(OutboundReportSaveContext<T, R> context) {
        // 默认实现，子类可重写
    }
    
    /**
     * 2. 前置处理
     * 执行初始化和准备工作
     */
    default void preProcess(OutboundReportSaveContext<T, R> context) {
        // 默认实现，子类可重写
    }
    
    /**
     * 3. 基本信息处理
     * 处理报告的基础信息，如质检码、操作人等
     */
    void processBasicInfo(OutboundReportSaveContext<T, R> context);
    
    /**
     * 4. 模板查询
     * 根据业务参数查询对应的质检模板
     */
    void queryTemplate(OutboundReportSaveContext<T, R> context);
    
    /**
     * 5. 报告生成
     * 根据模板和输入数据生成报告内容
     */
    void generateReport(OutboundReportSaveContext<T, R> context);
    
    /**
     * 6. 报告处理
     * 对生成的报告进行进一步加工处理
     */
    default void processReport(OutboundReportSaveContext<T, R> context) {
        // 默认实现，子类可重写
    }
    
    /**
     * 7. 报告映射
     * 将报告数据映射到返回的DTO对象中
     */
    void mapReport(OutboundReportSaveContext<T, R> context);
    
    /**
     * 8. 后置处理
     * 执行完成后的清理工作、发送通知等
     */
    void postProcess(OutboundReportSaveContext<T, R> context);
    
    /**
     * 9. 数据写入
     * 将处理结果持久化到数据库
     */
    default void writeData(OutboundReportSaveContext<T, R> context) {
        // 默认实现，子类可重写
    }
    
    /**
     * 异常处理
     * 当流程执行出现异常时的处理逻辑
     */
    default void handleException(OutboundReportSaveContext<T, R> context, Exception e) {
        // 默认实现，子类可重写
    }
    
    /**
     * 创建结果对象
     * 创建对应类型的返回DTO实例
     */
    R createResult();
}
