package com.zhuanzhuan.qc.report.outbound.domain.report.service.processor;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.ReportSaveContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 报告保存处理器
 * 负责协调策略执行各个步骤
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ReportSaveProcessor {

    /**
     * 1. 前置校验
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void preValidate(ReportSaveContext<T, R> context) {
            context.getStrategy().preValidate(context);
    }

    /**
     * 2. 前置处理
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void preProcess(ReportSaveContext<T, R> context) {
            context.getStrategy().preProcess(context);
    }

    /**
     * 3. 基本信息处理
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void processBasicInfo(ReportSaveContext<T, R> context) {
        log.debug("执行基本信息处理, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
        context.setCurrentStep("processBasicInfo");

        if (!context.shouldSkip("processBasicInfo")) {
            context.getStrategy().processBasicInfo(context);
        }
    }

    /**
     * 4. 模板查询
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void queryTemplate(ReportSaveContext<T, R> context) {
        log.debug("执行模板查询, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
        context.setCurrentStep("queryTemplate");

        if (!context.shouldSkip("queryTemplate")) {
            context.getStrategy().queryTemplate(context);
        }
    }

    /**
     * 5. 报告生成
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void generateReport(ReportSaveContext<T, R> context) {
        log.debug("执行报告生成, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
        context.setCurrentStep("generateReport");
        
        if (!context.shouldSkip("generateReport")) {
            context.getStrategy().generateReport(context);
        }
    }

    /**
     * 6. 报告处理
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void processReport(ReportSaveContext<T, R> context) {
        log.debug("执行报告处理, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
        context.setCurrentStep("processReport");
        
        if (!context.shouldSkip("processReport")) {
            context.getStrategy().processReport(context);
        }
    }

    /**
     * 7. 报告映射
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void mapReport(ReportSaveContext<T, R> context) {
        log.debug("执行报告映射, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
        context.setCurrentStep("mapReport");
        
        if (!context.shouldSkip("mapReport")) {
            context.getStrategy().mapReport(context);
        }
    }

    /**
     * 8. 后置处理
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void postProcess(ReportSaveContext<T, R> context) {
        log.debug("执行后置处理, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
        context.setCurrentStep("postProcess");
        
        if (!context.shouldSkip("postProcess")) {
            context.getStrategy().postProcess(context);
        }
    }

    /**
     * 9. 数据写入
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void writeData(ReportSaveContext<T, R> context) {
        log.debug("执行数据写入, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
        context.setCurrentStep("writeData");
        
        // 数据写入步骤通常不跳过，确保数据持久化
        // 这里可以添加具体的数据写入逻辑，或者委托给策略处理
        if (context.getResult() == null) {
            context.setResult(context.getStrategy().createResult());
        }
        
        // 设置基本返回信息
        if (context.getResult() != null) {
            context.getResult().setQcCode(context.getCommand().getQcCode());
            // 这里可以设置版本号等其他信息
        }
        
        log.info("报告保存完成, qcCode: {}, reportType: {}", context.getCommand().getQcCode(), context.getReportType());
    }
}
