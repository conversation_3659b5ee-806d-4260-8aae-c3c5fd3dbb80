package com.zhuanzhuan.qc.report.outbound.domain.report.service.template;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.saver.OutboundReportSaveContext;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.OutboundReportSaveStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 出库报告保存模板方法类
 * 定义统一的8步保存流程骨架，具体实现由策略模式处理
 * 
 * 流程步骤：
 * 1. 前置校验 (preValidate)
 * 2. 前置处理 (preProcess) 
 * 3. 基本信息处理 (processBasicInfo)
 * 4. 模板查询 (queryTemplate)
 * 5. 报告生成 (generateReport)
 * 6. 报告处理 (processReport)
 * 7. 报告映射 (mapReport)
 * 8. 后置处理 (postProcess)
 * 9. 数据写入 (writeData)
 */
@Slf4j
@Component
public class OutboundReportSaveTemplate {

    /**
     * 模板方法：定义出库报告保存的标准流程
     *
     * @param context  保存上下文
     * @param strategy 保存策略
     * @return 保存结果
     */
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> R saveReport(
            OutboundReportSaveContext<T, R> context, 
            OutboundReportSaveStrategy<T, R> strategy) {
        
        log.info("开始执行出库报告保存流程, qcCode: {}, reportType: {}", 
                context.getCommand().getQcCode(), context.getReportType());
        
        try {
            // 1. 前置校验
            executeStep(context, "preValidate", () -> strategy.preValidate(context));

            // 2. 前置处理
            executeStep(context, "preProcess", () -> strategy.preProcess(context));

            // 3. 基本信息处理
            executeStep(context, "processBasicInfo", () -> strategy.processBasicInfo(context));

            // 4. 模板查询
            executeStep(context, "queryTemplate", () -> strategy.queryTemplate(context));

            // 5. 报告生成
            executeStep(context, "generateReport", () -> strategy.generateReport(context));

            // 6. 报告处理
            executeStep(context, "processReport", () -> strategy.processReport(context));

            // 7. 报告映射
            executeStep(context, "mapReport", () -> strategy.mapReport(context));

            // 8. 后置处理
            executeStep(context, "postProcess", () -> strategy.postProcess(context));

            // 9. 数据写入
            executeStep(context, "writeData", () -> writeData(context, strategy));

            context.markSuccess();
            log.info("出库报告保存流程执行成功, qcCode: {}, reportType: {}, 耗时: {}ms", 
                    context.getCommand().getQcCode(), context.getReportType(), context.getElapsedTime());

            return context.getResult();

        } catch (Exception e) {
            context.markFailed(e.getMessage());
            log.error("出库报告保存流程执行失败, qcCode: {}, reportType: {}, currentStep: {}, 耗时: {}ms, error: {}", 
                    context.getCommand().getQcCode(), context.getReportType(), 
                    context.getCurrentStep(), context.getElapsedTime(), e.getMessage(), e);
            
            // 执行异常处理
            try {
                strategy.handleException(context, e);
            } catch (Exception handlerException) {
                log.error("异常处理器执行失败, qcCode: {}, error: {}", 
                        context.getCommand().getQcCode(), handlerException.getMessage(), handlerException);
            }
            
            throw new RuntimeException("出库报告保存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行单个步骤
     */
    private <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void executeStep(
            OutboundReportSaveContext<T, R> context, 
            String stepName, 
            Runnable stepAction) {
        
        if (context.shouldSkip(stepName)) {
            log.debug("跳过步骤: {}, qcCode: {}", stepName, context.getCommand().getQcCode());
            return;
        }
        
        context.setCurrentStep(stepName);
        log.debug("执行步骤: {}, qcCode: {}", stepName, context.getCommand().getQcCode());
        
        long stepStartTime = System.currentTimeMillis();
        stepAction.run();
        long stepElapsedTime = System.currentTimeMillis() - stepStartTime;
        
        log.debug("步骤执行完成: {}, qcCode: {}, 耗时: {}ms", 
                stepName, context.getCommand().getQcCode(), stepElapsedTime);
    }

    /**
     * 数据写入步骤
     */
    private <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> void writeData(
            OutboundReportSaveContext<T, R> context, 
            OutboundReportSaveStrategy<T, R> strategy) {
        
        // 确保结果对象存在
        if (context.getResult() == null) {
            context.setResult(strategy.createResult());
        }
        
        // 设置基本返回信息
        R result = context.getResult();
        if (result != null) {
            result.setQcCode(context.getCommand().getQcCode());
            // 这里可以设置版本号等其他通用信息
        }
        
        // 委托给策略进行具体的数据写入
        strategy.writeData(context);
        
        log.debug("数据写入完成, qcCode: {}", context.getCommand().getQcCode());
    }
}
