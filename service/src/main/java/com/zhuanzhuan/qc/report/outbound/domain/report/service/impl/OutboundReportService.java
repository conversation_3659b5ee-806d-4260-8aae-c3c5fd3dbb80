package com.zhuanzhuan.qc.report.outbound.domain.report.service.impl;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveCapturedPhotoPostSaleInspectionCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveOnsiteServiceCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveRecyclingReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveCapturedPhotoPostSaleInspectionDTO;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveOnsiteServiceDTO;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveRecyclingReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.saver.OutboundReportSaveContext;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.IOutboundReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class OutboundReportService implements IOutboundReportService {


    @Override
    public SaveRecyclingReportDTO saveRecyclingReport(SaveRecyclingReportCmd cmd) {
        return null;
    }

    @Override
    public SaveOnsiteServiceDTO saveOnsiteServiceReport(SaveOnsiteServiceCmd cmd) {
        log.info("开始保存上门售后报告, qcCode: {}, operatorId: {}", cmd.getQcCode(), cmd.getOperatorId());

        // 创建上下文
        OutboundReportSaveContext<SaveOnsiteServiceCmd, SaveOnsiteServiceDTO> context =
                new OutboundReportSaveContext<>(cmd);

        // 获取策略
        var strategy = strategyFactory.getStrategy(cmd.getReportType());

        // 执行保存流程
        return saveTemplate.saveReport(context, strategy);
    }

    @Override
    public SaveCapturedPhotoPostSaleInspectionDTO saveCapturedPhotoPostSaleInspectionReport(SaveCapturedPhotoPostSaleInspectionCmd cmd) {
        log.info("开始保存售前拍照质检报告, qcCode: {}, operatorId: {}", cmd.getQcCode(), cmd.getOperatorId());

        // 创建上下文
        OutboundReportSaveContext<SaveCapturedPhotoPostSaleInspectionCmd, SaveCapturedPhotoPostSaleInspectionDTO> context =
                new OutboundReportSaveContext<>(cmd);

        // 获取策略
        var strategy = strategyFactory.getStrategy(cmd.getReportType());

        // 执行保存流程
        return saveTemplate.saveReport(context, strategy);
    }

}
