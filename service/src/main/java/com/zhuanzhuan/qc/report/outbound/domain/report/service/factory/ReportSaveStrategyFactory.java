package com.zhuanzhuan.qc.report.outbound.domain.report.service.factory;

import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.BaseOutboundReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.BaseOutboundReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.ReportSaveStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 报告保存策略工厂
 * 负责管理和获取不同类型的报告保存策略
 */
@Slf4j
@Component
public class ReportSaveStrategyFactory {

    @Autowired
    private List<ReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO>> strategies;

    private final Map<String, ReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO>> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (ReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO> strategy : strategies) {
            String reportType = strategy.getSupportedReportType();
            strategyMap.put(reportType, strategy);
            log.info("注册报告保存策略: {} -> {}", reportType, strategy.getClass().getSimpleName());
        }
        log.info("报告保存策略工厂初始化完成，共注册 {} 个策略", strategyMap.size());
    }

    /**
     * 根据报告类型获取对应的保存策略
     *
     * @param reportType 报告类型
     * @return 对应的保存策略
     * @throws IllegalArgumentException 如果找不到对应的策略
     */
    @SuppressWarnings("unchecked")
    public <T extends BaseOutboundReportCmd, R extends BaseOutboundReportDTO> ReportSaveStrategy<T, R> getStrategy(String reportType) {
        ReportSaveStrategy<? extends BaseOutboundReportCmd, ? extends BaseOutboundReportDTO> strategy = strategyMap.get(reportType);
        if (strategy == null) {
            throw new IllegalArgumentException("未找到报告类型 [" + reportType + "] 对应的保存策略");
        }
        return (ReportSaveStrategy<T, R>) strategy;
    }

}
