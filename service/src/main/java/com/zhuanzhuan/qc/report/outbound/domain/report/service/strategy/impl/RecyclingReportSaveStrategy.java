package com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.impl;

import com.zhuanzhuan.qc.report.outbound.contract.enums.report.EOutboundReportType;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.command.SaveRecyclingReportCmd;
import com.zhuanzhuan.qc.report.outbound.contract.model.report.result.SaveRecyclingReportDTO;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.context.OutboundReportSaveContext;
import com.zhuanzhuan.qc.report.outbound.domain.report.service.strategy.AbstractOutboundReportSaveStrategy;
import com.zhuanzhuan.qc.report.outbound.infrastructure.manager.IQcTemplateManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 回收质检报告保存策略
 * 实现回收报告的8步保存流程
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RecyclingReportSaveStrategy extends AbstractOutboundReportSaveStrategy<SaveRecyclingReportCmd, SaveRecyclingReportDTO> {

    private final IQcTemplateManager qcTemplateManager;

    @Override
    public EOutboundReportType getSupportedReportType() {
        return EOutboundReportType.RECYCLING_REPORT;
    }

    @Override
    protected void doPreValidate(OutboundReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.debug("执行回收报告特定前置校验, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        
        // 回收报告特有的校验逻辑
        // 例如：校验回收订单状态、商品信息等
        
        log.debug("回收报告特定前置校验完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void processBasicInfo(OutboundReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("处理回收质检报告基本信息, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        
        // 处理回收报告特有的基本信息
        // 例如：获取商品信息、回收订单信息、设备基本参数等
        context.setAttribute("productInfo", "回收商品信息"); // 示例
        context.setAttribute("recycleOrderInfo", "回收订单信息"); // 示例
        
        log.info("回收质检报告基本信息处理完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void queryTemplate(OutboundReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("查询回收质检模板, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        
        try {
            // 根据业务参数查询质检模板
            // 这里需要根据实际业务逻辑获取模板查询所需的参数
            Long businessLineId = command.getBusinessLineId();
            Integer cateId = context.getAttribute("cateId", 1); // 从商品信息中获取
            Integer brandId = context.getAttribute("brandId", 1); // 从商品信息中获取
            Integer modelId = context.getAttribute("modelId", 1); // 从商品信息中获取
            
            var template = qcTemplateManager.getRecyclingQcTemplate(businessLineId, cateId, brandId, modelId);
            context.setTemplate(template);
            
            log.info("回收质检模板查询成功, qcCode: {}, templateId: {}", 
                    command.getQcCode(), template != null ? template.getTemplateId() : null);
        } catch (Exception e) {
            log.error("查询回收质检模板失败, qcCode: {}, error: {}", command.getQcCode(), e.getMessage(), e);
            throw new RuntimeException("查询质检模板失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void generateReport(OutboundReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("生成回收质检报告, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        var template = context.getTemplate();
        
        if (template == null) {
            throw new IllegalStateException("质检模板不能为空");
        }
        
        // 根据模板和输入数据生成报告内容
        // 1. 解析模板结构
        // 2. 处理质检项目和结果
        // 3. 计算评分和等级
        // 4. 生成报告摘要
        
        var reportData = new java.util.HashMap<String, Object>();
        reportData.put("qcCode", command.getQcCode());
        reportData.put("templateId", template.getTemplateId());
        reportData.put("businessLineId", command.getBusinessLineId());
        reportData.put("operatorId", command.getOperatorId());
        reportData.put("operatorName", command.getOperatorName());
        reportData.put("reportType", "recycling");
        reportData.put("generateTime", System.currentTimeMillis());
        
        // 回收报告特有的数据
        reportData.put("recycleType", "standard"); // 回收类型
        reportData.put("qualityGrade", "A"); // 质量等级
        reportData.put("estimatedPrice", 1000.0); // 预估价格
        
        context.setReportData(reportData);
        
        log.info("回收质检报告生成完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void mapReport(OutboundReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("映射回收质检报告结果, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        SaveRecyclingReportDTO result = context.getResult();
        var reportData = context.getReportData();
        
        if (result == null) {
            result = createResult();
            context.setResult(result);
        }
        
        // 映射基本信息
        result.setQcCode(command.getQcCode());
        
        // 映射报告特有信息
        if (reportData instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            var dataMap = (java.util.Map<String, Object>) reportData;
            
            // 可以从reportData中提取更多信息设置到result中
            // 例如：设置版本号、状态、评分等
        }
        
        log.info("回收质检报告结果映射完成, qcCode: {}", command.getQcCode());
    }

    @Override
    public void postProcess(OutboundReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.info("回收质检报告后置处理, qcCode: {}", context.getCommand().getQcCode());
        
        SaveRecyclingReportCmd command = context.getCommand();
        
        // 回收报告特有的后置处理逻辑
        // 1. 发送质检完成通知
        // 2. 更新回收订单状态
        // 3. 触发价格计算流程
        // 4. 记录操作日志
        
        context.setAttribute("postProcessed", true);
        context.setAttribute("notificationSent", true);
        
        log.info("回收质检报告后置处理完成, qcCode: {}", command.getQcCode());
    }

    @Override
    protected void doWriteData(OutboundReportSaveContext<SaveRecyclingReportCmd, SaveRecyclingReportDTO> context) {
        log.debug("执行回收报告数据写入, qcCode: {}", context.getCommand().getQcCode());
        
        // 回收报告特有的数据写入逻辑
        // 1. 保存报告主表数据
        // 2. 保存报告扩展数据
        // 3. 保存质检项目详情
        // 4. 更新相关业务状态
        
        log.debug("回收报告数据写入完成, qcCode: {}", context.getCommand().getQcCode());
    }

    @Override
    public SaveRecyclingReportDTO createResult() {
        return new SaveRecyclingReportDTO();
    }
}
